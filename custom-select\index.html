<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Custom Select Components</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="./main.css" />
  </head>
  <body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto space-y-8">
      <h1 class="text-3xl font-bold text-gray-800 mb-8">
        Custom Select Components
      </h1>

      <!-- Multi-Select Examples -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-6">Multi-Select Dropdown</h2>

        <!-- Example 1: Basic Multi-Select -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2"
            >Service/Product Offered</label
          >
          <select
            id="multi-select-1"
            multiple
            class="hidden"
            data-select-type="multi"
            data-placeholder="Select services..."
          >
            <option value="health">Health Screening</option>
            <option value="opt1" selected>Option 1</option>
            <option value="opt2" selected>Option 2</option>
            <option value="opt3">Option 3</option>
            <option value="opt4">Option 4</option>
            <option value="opt5">Option 5</option>
            <option value="opt6">Option 6</option>
            <option value="opt7">Option 7</option>
            <option value="opt8">Option 8</option>
            <option value="opt9">Option 9</option>
            <option value="opt10">Option 10</option>
          </select>
        </div>
      </div>

      <!-- Single-Select Examples -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-6">Single-Select Dropdown</h2>

        <!-- Example 1: Basic Single-Select -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2"
            >Select Product</label
          >
          <select
            id="single-select-1"
            class="hidden"
            data-select-type="single"
            data-placeholder="Choose a product"
          >
            <option value="">Choose a product</option>
            <option value="health">Health Screening</option>
            <option value="opt1">Option 1</option>
            <option value="opt2">Option 2</option>
            <option value="opt3">Option 3</option>
            <option value="opt4">Option 4</option>
            <option value="opt5">Option 5</option>
            <option value="opt6">Option 6</option>
            <option value="opt7">Option 7</option>
            <option value="opt8">Option 8</option>
            <option value="opt9">Option 9</option>
            <option value="opt10">Option 10</option>
            <option value="opt11">Option 11</option>
            <option value="opt12">Option 12</option>
            <option value="opt13">Option 13</option>
            <option value="opt14">Option 14</option>
            <option value="opt15" selected>Option 15</option>
            <option value="opt16">Option 16</option>
            <option value="opt17">Option 17</option>
            <option value="opt18">Option 18</option>
            <option value="opt19">Option 19</option>
            <option value="opt20">Option 20</option>
          </select>
        </div>

        <!-- Test Multiple Dropdowns -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2"
            >Test Multiple Dropdowns (Option 10 selected)</label
          >
          <select
            id="single-select-2"
            class="hidden"
            data-select-type="single"
            data-placeholder="Choose another option"
          >
            <option value="">Choose another option</option>
            <option value="test1">Test Option 1</option>
            <option value="test2">Test Option 2</option>
            <option value="test3">Test Option 3</option>
            <option value="test4">Test Option 4</option>
            <option value="test5">Test Option 5</option>
            <option value="test6">Test Option 6</option>
            <option value="test7">Test Option 7</option>
            <option value="test8">Test Option 8</option>
            <option value="test9">Test Option 9</option>
            <option value="test10" selected>Test Option 10</option>
            <option value="test11">Test Option 11</option>
            <option value="test12">Test Option 12</option>
          </select>
        </div>
      </div>

      <!-- Form Integration Example -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-6">Form Integration Example</h2>
        <form id="demo-form" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Department</label
              >
              <select
                id="department-select"
                class="hidden"
                data-select-type="single"
                data-placeholder="Select Department"
              >
                <option value="">Select Department</option>
                <option value="hr">Human Resources</option>
                <option value="it">Information Technology</option>
                <option value="finance">Finance</option>
                <option value="marketing">Marketing</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Skills</label
              >
              <select
                id="skills-select"
                multiple
                class="hidden"
                data-select-type="multi"
                data-placeholder="Select your skills..."
              >
                <option value="js">JavaScript</option>
                <option value="python">Python</option>
                <option value="react">React</option>
                <option value="node">Node.js</option>
                <option value="sql">SQL</option>
              </select>
            </div>
          </div>
          <button
            type="submit"
            class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Submit Form
          </button>
        </form>
      </div>

      <!-- Event Handling Demo -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Event Handling Demo</h2>
        <div
          id="event-log"
          class="bg-gray-50 p-4 rounded border h-32 overflow-y-auto text-sm font-mono"
        >
          <div class="text-gray-500">Events will appear here...</div>
        </div>
      </div>
    </div>

    <script src="./js/custom-select.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Selects are auto-initialized via data-select-type attribute
        // Get references to the select instances if needed
        const multiSelect1 = document.getElementById("multi-select-1");
        const skillsSelect = document.getElementById("skills-select");
        const singleSelect1 = document.getElementById("single-select-1");
        const departmentSelect = document.getElementById("department-select");

        const eventLog = document.getElementById("event-log");

        function logEvent(message) {
          const timestamp = new Date().toLocaleTimeString();
          const logEntry = document.createElement("div");
          logEntry.textContent = `[${timestamp}] ${message}`;
          eventLog.appendChild(logEntry);
          eventLog.scrollTop = eventLog.scrollHeight;
        }

        // Add event listeners for demonstration
        multiSelect1.addEventListener("change", (e) => {
          logEvent(
            `Multi-select 1: ${e.detail.selectedValues.length} items selected`
          );
        });

        singleSelect1.addEventListener("change", (e) => {
          logEvent(
            `Single-select 1: ${
              e.detail.selectedOption?.label || "None"
            } selected`
          );
        });

        skillsSelect.addEventListener("change", (e) => {
          logEvent(
            `Skills: ${e.detail.selectedOptions.map((o) => o.label).join(", ")}`
          );
        });

        departmentSelect.addEventListener("change", (e) => {
          logEvent(
            `Department: ${e.detail.selectedOption?.label || "None"} selected`
          );
        });

        // Form submission demo
        document.getElementById("demo-form").addEventListener("submit", (e) => {
          e.preventDefault();
          const department = departmentSelect.value;
          const skills = Array.from(skillsSelect.selectedOptions).map(
            (opt) => opt.value
          );
          logEvent(
            `Form submitted - Department: ${department}, Skills: ${skills.join(
              ", "
            )}`
          );
        });
      });
    </script>
  </body>
</html>
