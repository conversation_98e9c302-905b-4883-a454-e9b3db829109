<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Custom Select Components</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="./main.css" />
  </head>
  <body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto space-y-8">
      <h1 class="text-3xl font-bold text-gray-800 mb-8">
        Custom Select Components
      </h1>

      <!-- New Features Showcase -->
      <div class="bg-blue-50 border border-blue-200 p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold text-blue-800 mb-4">
          ✨ Enhanced User Experience Features
        </h2>
        <div class="grid md:grid-cols-2 gap-4 text-sm text-blue-700">
          <div class="space-y-2">
            <div class="flex items-start gap-2">
              <span class="text-green-600 font-bold">✓</span>
              <span
                ><strong>Only One Dropdown Open:</strong> Opening a new dropdown
                automatically closes others, preventing confusion</span
              >
            </div>
            <div class="flex items-start gap-2">
              <span class="text-green-600 font-bold">✓</span>
              <span
                ><strong>Auto-scroll to Selected:</strong> Selected options are
                immediately visible when opening dropdowns</span
              >
            </div>
          </div>
          <div class="space-y-2">
            <div class="flex items-start gap-2">
              <span class="text-green-600 font-bold">✓</span>
              <span
                ><strong>Memory Efficient:</strong> Automatic cleanup prevents
                memory leaks and handles DOM changes</span
              >
            </div>
            <div class="flex items-start gap-2">
              <span class="text-green-600 font-bold">✓</span>
              <span
                ><strong>Consistent Behavior:</strong> Works the same way across
                all dropdown instances</span
              >
            </div>
          </div>
        </div>
        <div class="mt-4 p-3 bg-blue-100 rounded border-l-4 border-blue-400">
          <p class="text-blue-800 text-sm">
            <strong>Try it:</strong> Open multiple dropdowns below to see how
            only one stays open at a time. Notice how selected options are
            automatically scrolled into view!
          </p>
        </div>
      </div>

      <!-- Multi-Select Examples -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-6">Multi-Select Dropdown</h2>

        <!-- Example 1: Basic Multi-Select -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2"
            >Service/Product Offered</label
          >
          <select
            id="multi-select-1"
            multiple
            class="hidden"
            data-select-type="multi"
            data-placeholder="Select services..."
          >
            <option value="health">Health Screening</option>
            <option value="opt1" selected>Option 1</option>
            <option value="opt2" selected>Option 2</option>
            <option value="opt3">Option 3</option>
            <option value="opt4">Option 4</option>
            <option value="opt5">Option 5</option>
            <option value="opt6">Option 6</option>
            <option value="opt7">Option 7</option>
            <option value="opt8">Option 8</option>
            <option value="opt9">Option 9</option>
            <option value="opt10">Option 10</option>
          </select>
        </div>
      </div>

      <!-- Single-Select Examples -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-6">Single-Select Dropdown</h2>

        <!-- Example 1: Basic Single-Select -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2"
            >Select Product</label
          >
          <select
            id="single-select-1"
            class="hidden"
            data-select-type="single"
            data-placeholder="Choose a product"
          >
            <option value="">Choose a product</option>
            <option value="health">Health Screening</option>
            <option value="opt1">Option 1</option>
            <option value="opt2">Option 2</option>
            <option value="opt3">Option 3</option>
            <option value="opt4">Option 4</option>
            <option value="opt5">Option 5</option>
            <option value="opt6">Option 6</option>
            <option value="opt7">Option 7</option>
            <option value="opt8">Option 8</option>
            <option value="opt9">Option 9</option>
            <option value="opt10">Option 10</option>
            <option value="opt11">Option 11</option>
            <option value="opt12">Option 12</option>
            <option value="opt13">Option 13</option>
            <option value="opt14">Option 14</option>
            <option value="opt15" selected>Option 15</option>
            <option value="opt16">Option 16</option>
            <option value="opt17">Option 17</option>
            <option value="opt18">Option 18</option>
            <option value="opt19">Option 19</option>
            <option value="opt20">Option 20</option>
          </select>
        </div>

        <!-- Test Multiple Dropdowns -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2"
            >Test Multiple Dropdowns (Option 10 selected)</label
          >
          <select
            id="single-select-2"
            class="hidden"
            data-select-type="single"
            data-placeholder="Choose another option"
          >
            <option value="">Choose another option</option>
            <option value="test1">Test Option 1</option>
            <option value="test2">Test Option 2</option>
            <option value="test3">Test Option 3</option>
            <option value="test4">Test Option 4</option>
            <option value="test5">Test Option 5</option>
            <option value="test6">Test Option 6</option>
            <option value="test7">Test Option 7</option>
            <option value="test8">Test Option 8</option>
            <option value="test9">Test Option 9</option>
            <option value="test10" selected>Test Option 10</option>
            <option value="test11">Test Option 11</option>
            <option value="test12">Test Option 12</option>
          </select>
        </div>
      </div>

      <!-- Form Integration Example -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-6">Form Integration Example</h2>
        <form id="demo-form" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Department</label
              >
              <select
                id="department-select"
                class="hidden"
                data-select-type="single"
                data-placeholder="Select Department"
              >
                <option value="">Select Department</option>
                <option value="hr">Human Resources</option>
                <option value="it">Information Technology</option>
                <option value="finance">Finance</option>
                <option value="marketing">Marketing</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Skills</label
              >
              <select
                id="skills-select"
                multiple
                class="hidden"
                data-select-type="multi"
                data-placeholder="Select your skills..."
              >
                <option value="js">JavaScript</option>
                <option value="python">Python</option>
                <option value="react">React</option>
                <option value="node">Node.js</option>
                <option value="sql">SQL</option>
              </select>
            </div>
          </div>
          <button
            type="submit"
            class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Submit Form
          </button>
        </form>
      </div>

      <!-- Enhanced UX Demonstration -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">🎯 Enhanced UX Demonstration</h2>
        <p class="text-gray-600 mb-6">
          Try opening multiple dropdowns below to see the improved behavior in
          action:
        </p>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Demo Dropdown 1 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Demo Multi-Select 1
            </label>
            <select
              id="demo-multi-1"
              multiple
              class="hidden"
              data-select-type="multi"
              data-placeholder="Select items..."
            >
              <option value="item1">Item 1</option>
              <option value="item2" selected>Item 2 (Selected)</option>
              <option value="item3">Item 3</option>
              <option value="item4">Item 4</option>
              <option value="item5" selected>Item 5 (Selected)</option>
              <option value="item6">Item 6</option>
              <option value="item7">Item 7</option>
              <option value="item8">Item 8</option>
              <option value="item9">Item 9</option>
              <option value="item10">Item 10</option>
            </select>
          </div>

          <!-- Demo Dropdown 2 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Demo Single-Select
            </label>
            <select
              id="demo-single-1"
              class="hidden"
              data-select-type="single"
              data-placeholder="Choose option..."
            >
              <option value="">Choose option...</option>
              <option value="alpha">Alpha</option>
              <option value="beta">Beta</option>
              <option value="gamma">Gamma</option>
              <option value="delta">Delta</option>
              <option value="epsilon">Epsilon</option>
              <option value="zeta">Zeta</option>
              <option value="eta">Eta</option>
              <option value="theta" selected>Theta (Selected)</option>
              <option value="iota">Iota</option>
              <option value="kappa">Kappa</option>
            </select>
          </div>

          <!-- Demo Dropdown 3 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Demo Multi-Select 2
            </label>
            <select
              id="demo-multi-2"
              multiple
              class="hidden"
              data-select-type="multi"
              data-placeholder="Pick options..."
            >
              <option value="red">Red</option>
              <option value="orange">Orange</option>
              <option value="yellow">Yellow</option>
              <option value="green" selected>Green (Selected)</option>
              <option value="blue">Blue</option>
              <option value="indigo">Indigo</option>
              <option value="violet" selected>Violet (Selected)</option>
              <option value="pink">Pink</option>
              <option value="brown">Brown</option>
              <option value="black">Black</option>
            </select>
          </div>
        </div>

        <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
          <h3 class="font-semibold text-yellow-800 mb-2">What to Notice:</h3>
          <ul class="text-sm text-yellow-700 space-y-1">
            <li>
              • <strong>Global Management:</strong> Only one dropdown can be
              open at a time
            </li>
            <li>
              • <strong>Auto-scroll:</strong> Selected options are immediately
              visible when opening
            </li>
            <li>
              • <strong>Smooth Experience:</strong> No need to manually scroll
              to find your selections
            </li>
            <li>
              • <strong>Memory Efficient:</strong> Proper cleanup when dropdowns
              are closed or destroyed
            </li>
          </ul>
        </div>
      </div>

      <!-- Event Handling Demo -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Event Handling Demo</h2>
        <div
          id="event-log"
          class="bg-gray-50 p-4 rounded border h-32 overflow-y-auto text-sm font-mono"
        >
          <div class="text-gray-500">Events will appear here...</div>
        </div>
      </div>
    </div>

    <script src="./js/custom-select.js"></script>
    <!-- <script src="./js/custom-select.min.js"></script> -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Selects are auto-initialized via data-select-type attribute
        // Get references to the select instances if needed
        const multiSelect1 = document.getElementById("multi-select-1");
        const skillsSelect = document.getElementById("skills-select");
        const singleSelect1 = document.getElementById("single-select-1");
        const departmentSelect = document.getElementById("department-select");

        const eventLog = document.getElementById("event-log");

        function logEvent(message) {
          const timestamp = new Date().toLocaleTimeString();
          const logEntry = document.createElement("div");
          logEntry.textContent = `[${timestamp}] ${message}`;
          eventLog.appendChild(logEntry);
          eventLog.scrollTop = eventLog.scrollHeight;
        }

        // Add event listeners for demonstration
        multiSelect1.addEventListener("change", (e) => {
          logEvent(
            `Multi-select 1: ${e.detail.selectedValues.length} items selected`
          );
        });

        singleSelect1.addEventListener("change", (e) => {
          logEvent(
            `Single-select 1: ${
              e.detail.selectedOption?.label || "None"
            } selected`
          );
        });

        skillsSelect.addEventListener("change", (e) => {
          logEvent(
            `Skills: ${e.detail.selectedOptions.map((o) => o.label).join(", ")}`
          );
        });

        departmentSelect.addEventListener("change", (e) => {
          logEvent(
            `Department: ${e.detail.selectedOption?.label || "None"} selected`
          );
        });

        // Form submission demo
        document.getElementById("demo-form").addEventListener("submit", (e) => {
          e.preventDefault();
          const department = departmentSelect.value;
          const skills = Array.from(skillsSelect.selectedOptions).map(
            (opt) => opt.value
          );
          logEvent(
            `Form submitted - Department: ${department}, Skills: ${skills.join(
              ", "
            )}`
          );
        });
      });
    </script>
  </body>
</html>
