/**
 * CustomSelect - Unified Single and Multi-Select Component
 * Supports both single and multi-select dropdowns with keyboard navigation
 */
class CustomSelect {
  static instances = new Set();

  constructor(element, options = {}) {
    this.element = element;
    this.isMultiple = element.hasAttribute('multiple') || element.dataset.selectType === 'multi';

    this.options = {
      placeholder: this.isMultiple ? 'Select options' : 'Select an option',
      searchable: false,
      selectAll: true,
      maxHeight: '260px',
      ...options
    };

    this.selectedValues = this.isMultiple ? [] : null;
    this.allOptions = [];
    this.isOpen = false;
    this.focusedIndex = -1;

    // Register this instance globally
    CustomSelect.instances.add(this);

    this.init();
  }

  init() {
    this.parseOptions();
    this.createDropdown();
    this.attachEventListeners();
    this.updateDisplay();
  }

  parseOptions() {
    // Parse options from select element or data attribute
    if (this.element.tagName === 'SELECT') {
      const options = Array.from(this.element.querySelectorAll('option'));
      this.allOptions = options.map(opt => ({
        value: opt.value || opt.textContent.trim(),
        label: opt.textContent.trim(),
        selected: opt.selected
      }));
    } else if (this.element.dataset.options) {
      this.allOptions = JSON.parse(this.element.dataset.options);
    } else {
      this.allOptions = this.options.options || [];
    }

    // Set initially selected values
    if (this.isMultiple) {
      this.selectedValues = this.allOptions
        .filter(opt => opt.selected)
        .map(opt => opt.value);
    } else {
      const selectedOption = this.allOptions.find(opt => opt.selected);
      this.selectedValues = selectedOption ? selectedOption.value : null;
    }
  }

  createDropdown() {
    // Hide original element
    this.element.style.display = 'none';

    // Create container
    this.container = document.createElement('div');
    this.container.className = `custom-select-container ${this.isMultiple ? 'multi' : 'single'}`;

    // Create trigger
    this.trigger = document.createElement('div');
    this.trigger.className = 'custom-select-trigger';
    this.trigger.setAttribute('role', 'button');
    this.trigger.setAttribute('aria-haspopup', 'listbox');
    this.trigger.setAttribute('tabindex', '0');

    this.triggerText = document.createElement('span');
    this.triggerText.className = 'custom-select-text';

    this.arrow = document.createElement('span');
    this.arrow.className = 'custom-select-arrow';
    this.arrow.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
        <path d="M1 1.5L6 6.5L11 1.5" stroke="currentColor" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `;

    this.trigger.appendChild(this.triggerText);
    this.trigger.appendChild(this.arrow);

    // Create dropdown
    this.dropdown = document.createElement('div');
    this.dropdown.className = 'custom-select-dropdown';
    this.dropdown.setAttribute('role', 'listbox');
    if (this.isMultiple) {
      this.dropdown.setAttribute('aria-multiselectable', 'true');
    }

    // Create chips container for multi-select
    if (this.isMultiple) {
      this.chipsContainer = document.createElement('div');
      this.chipsContainer.className = 'custom-select-chips';
    }

    // Assemble
    this.container.appendChild(this.trigger);
    this.container.appendChild(this.dropdown);
    if (this.isMultiple) {
      this.container.appendChild(this.chipsContainer);
    }

    // Insert after original element
    this.element.parentNode.insertBefore(this.container, this.element.nextSibling);

    this.renderDropdown();
  }

  renderDropdown() {
    // Save scroll position before re-rendering
    const wrapper = this.dropdown.querySelector('.custom-select-items-wrapper');
    const scrollPosition = wrapper ? wrapper.scrollTop : 0;

    let html = '';

    // Add select all option if enabled (multi-select only)
    if (this.isMultiple && this.options.selectAll && this.allOptions.length > 1) {
      const allSelected = this.selectedValues.length === this.allOptions.length;
      html += `
        <div class="custom-select-item custom-select-select-all" role="option" data-value="__select_all__" data-index="-1">
          <input type="checkbox" id="select-all-${this.element.id}" class="custom-select-checkbox" ${allSelected ? 'checked' : ''} tabindex="-1">
          <label for="select-all-${this.element.id}" class="custom-select-item-text">Select All</label>
        </div>
      `;
    }

    // Add options
    this.allOptions.forEach((option, index) => {
      const isSelected = this.isMultiple
        ? this.selectedValues.includes(option.value)
        : this.selectedValues === option.value;

      if (this.isMultiple) {
        const checkboxId = `${this.element.id}-option-${index}`;
        html += `
          <div class="custom-select-item ${isSelected ? 'selected' : ''}" role="option" data-value="${option.value}" data-index="${index}">
            <input type="checkbox" id="${checkboxId}" class="custom-select-checkbox" ${isSelected ? 'checked' : ''} tabindex="-1">
            <label for="${checkboxId}" class="custom-select-item-text">${option.label}</label>
          </div>
        `;
      } else {
        html += `
          <div class="custom-select-item ${isSelected ? 'selected' : ''}"
               role="option"
               data-value="${option.value}"
               data-index="${index}"
               aria-selected="${isSelected}">
            <span class="custom-select-item-text">${option.label}</span>
          </div>
        `;
      }
    });

    // Wrap items in a container div
    this.dropdown.innerHTML = `<div class="custom-select-items-wrapper">${html}</div>`;

    // Restore scroll position after re-rendering
    const newWrapper = this.dropdown.querySelector('.custom-select-items-wrapper');
    if (newWrapper && scrollPosition > 0) {
      newWrapper.scrollTop = scrollPosition;
    }

    // Restore focus state after re-rendering
    if (this.isOpen && this.focusedIndex >= 0) {
      this.updateFocus();
    }
  }

  attachEventListeners() {
    // Bind methods for proper cleanup
    this.boundToggle = (e) => {
      e.stopPropagation();
      this.toggle();
    };

    this.boundHandleDropdownClick = (e) => {
      e.stopPropagation();
      const item = e.target.closest('.custom-select-item');
      if (item) {
        this.handleItemClick(item);
      }
    };

    this.boundHandleKeyDown = (e) => {
      this.handleKeyDown(e);
    };

    this.boundHandleDocumentClick = () => {
      this.close();
    };

    this.boundHandleChipClick = (e) => {
      if (e.target.classList.contains('custom-select-chip-remove')) {
        const value = e.target.dataset.value;
        this.toggleOption(value, false);
      }
    };

    // Trigger click
    this.trigger.addEventListener('click', this.boundToggle);

    // Dropdown click
    this.dropdown.addEventListener('click', this.boundHandleDropdownClick);

    // Keyboard support
    this.trigger.addEventListener('keydown', this.boundHandleKeyDown);

    // Keyboard navigation in dropdown
    this.dropdown.addEventListener('keydown', this.boundHandleKeyDown);

    // Click outside to close
    document.addEventListener('click', this.boundHandleDocumentClick);

    // Chip removal (multi-select only)
    if (this.isMultiple) {
      this.chipsContainer.addEventListener('click', this.boundHandleChipClick);
    }
  }

  handleKeyDown(e) {
    switch (e.key) {
      case 'Enter':
      case ' ':
        e.preventDefault();
        e.stopPropagation();
        if (!this.isOpen) {
          this.open();
        } else if (this.focusedIndex >= 0) {
          // Select the focused item
          const items = this.dropdown.querySelectorAll('.custom-select-item');
          const focusedItem = items[this.focusedIndex];
          if (focusedItem) {
            this.handleItemClick(focusedItem);
            if (!this.isMultiple) {
              this.close();
            }
          }
        } else {
          this.toggle();
        }
        break;

      case 'ArrowDown':
        e.preventDefault();
        if (!this.isOpen) {
          this.open();
        } else {
          this.focusNext();
        }
        break;

      case 'ArrowUp':
        e.preventDefault();
        if (this.isOpen) {
          this.focusPrevious();
        }
        break;

      case 'Escape':
        e.preventDefault();
        this.close();
        this.trigger.focus();
        break;

      case 'Tab':
        if (this.isOpen) {
          this.close();
        }
        break;
    }
  }

  handleItemClick(item) {
    const value = item.dataset.value;

    // Update focused index to the clicked item for proper keyboard navigation
    const items = this.dropdown.querySelectorAll('.custom-select-item');
    const clickedIndex = Array.from(items).indexOf(item);
    if (clickedIndex >= 0) {
      this.focusedIndex = clickedIndex;
      this.updateFocus();
    }

    if (value === '__select_all__') {
      this.toggleSelectAll();
    } else if (this.isMultiple) {
      const checkbox = item.querySelector('.custom-select-checkbox');
      this.toggleOption(value, !checkbox.checked);
    } else {
      this.selectOption(value);
      this.close();
    }
  }

  toggleSelectAll() {
    const allSelected = this.selectedValues.length === this.allOptions.length;

    if (allSelected) {
      this.selectedValues = [];
    } else {
      this.selectedValues = this.allOptions.map(opt => opt.value);
    }

    this.updateDisplay();
    this.renderDropdown();
    this.dispatchChangeEvent();
  }

  toggleOption(value, selected) {
    if (selected && !this.selectedValues.includes(value)) {
      this.selectedValues.push(value);
    } else if (!selected) {
      this.selectedValues = this.selectedValues.filter(v => v !== value);
    }

    this.updateDisplay();
    this.renderDropdown();
    this.dispatchChangeEvent();
  }

  selectOption(value) {
    this.selectedValues = value;
    this.updateDisplay();
    this.renderDropdown();
    this.dispatchChangeEvent();
  }

  updateDisplay() {
    if (this.isMultiple) {
      // Update trigger text for multi-select
      const count = this.selectedValues.length;
      if (count === 0) {
        this.triggerText.textContent = this.options.placeholder;
        this.triggerText.classList.add('placeholder');
      } else {
        this.triggerText.textContent = `${count} selected`;
        this.triggerText.classList.remove('placeholder');
      }

      // Update chips
      this.updateChips();

      // Update original select element
      if (this.element.tagName === 'SELECT') {
        Array.from(this.element.options).forEach(option => {
          option.selected = this.selectedValues.includes(option.value);
        });
      }
    } else {
      // Update trigger text for single-select
      const selectedOption = this.allOptions.find(opt => opt.value === this.selectedValues);
      if (selectedOption) {
        this.triggerText.textContent = selectedOption.label;
        this.triggerText.classList.remove('placeholder');
      } else {
        this.triggerText.textContent = this.options.placeholder;
        this.triggerText.classList.add('placeholder');
      }

      // Update original select element
      if (this.element.tagName === 'SELECT') {
        this.element.value = this.selectedValues || '';
        Array.from(this.element.options).forEach(option => {
          option.selected = option.value === this.selectedValues;
        });
      }
    }
  }

  updateChips() {
    this.chipsContainer.innerHTML = '';

    this.selectedValues.forEach(value => {
      const option = this.allOptions.find(opt => opt.value === value);
      if (option) {
        const chip = document.createElement('div');
        chip.className = 'custom-select-chip';
        chip.innerHTML = `
          ${option.label}
          <span class="custom-select-chip-remove" data-value="${value}">×</span>
        `;
        this.chipsContainer.appendChild(chip);
      }
    });
  }

  focusNext() {
    const items = this.dropdown.querySelectorAll('.custom-select-item');
    const maxIndex = items.length - 1;

    if (this.focusedIndex < maxIndex) {
      this.focusedIndex++;
    } else {
      this.focusedIndex = 0; // Wrap around
    }

    this.updateFocus();
    this.scrollToFocused();
  }

  focusPrevious() {
    const items = this.dropdown.querySelectorAll('.custom-select-item');
    const maxIndex = items.length - 1;

    if (this.focusedIndex > 0) {
      this.focusedIndex--;
    } else {
      this.focusedIndex = maxIndex; // Wrap around
    }

    this.updateFocus();
    this.scrollToFocused();
  }

  updateFocus() {
    const items = this.dropdown.querySelectorAll('.custom-select-item');
    items.forEach((item, index) => {
      item.classList.toggle('focused', index === this.focusedIndex);
    });
  }

  scrollToFocused() {
    const items = this.dropdown.querySelectorAll('.custom-select-item');
    const focusedItem = items[this.focusedIndex];
    if (focusedItem) {
      const wrapper = this.dropdown.querySelector('.custom-select-items-wrapper');
      if (wrapper) {
        // Calculate positions
        const wrapperRect = wrapper.getBoundingClientRect();
        const itemRect = focusedItem.getBoundingClientRect();

        // Check if item is fully visible
        const isFullyVisible =
          itemRect.top >= wrapperRect.top &&
          itemRect.bottom <= wrapperRect.bottom;

        if (!isFullyVisible) {
          // Scroll to make the item visible with some padding
          const itemOffsetTop = focusedItem.offsetTop;
          const wrapperHeight = wrapper.clientHeight;
          const itemHeight = focusedItem.offsetHeight;

          // Add padding (20px) for better visibility
          const padding = 20;

          if (itemRect.top < wrapperRect.top) {
            // Item is above visible area - scroll up
            wrapper.scrollTop = itemOffsetTop - padding;
          } else if (itemRect.bottom > wrapperRect.bottom) {
            // Item is below visible area - scroll down
            wrapper.scrollTop = itemOffsetTop - wrapperHeight + itemHeight + padding;
          }
        }
      } else {
        // Fallback to standard scrollIntoView
        focusedItem.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
      }
    }
  }

  toggle() {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  open() {
    // Close all other dropdowns first (global dropdown management)
    CustomSelect.closeAllExcept(this);

    this.isOpen = true;
    this.dropdown.classList.add('open');
    this.trigger.classList.add('open');
    this.trigger.setAttribute('aria-expanded', 'true');

    // Set focus to selected item or first item and scroll to it
    if (this.isMultiple) {
      // For multi-select, focus on first selected item if any, otherwise first item
      const firstSelectedIndex = this.allOptions.findIndex(opt =>
        this.selectedValues.includes(opt.value)
      );
      this.focusedIndex = firstSelectedIndex >= 0 ? firstSelectedIndex : 0;
    } else {
      // For single-select, focus on selected item if any, otherwise first item
      const selectedIndex = this.allOptions.findIndex(opt => opt.value === this.selectedValues);
      this.focusedIndex = selectedIndex >= 0 ? selectedIndex : 0;
    }

    this.updateFocus();

    // Use a small delay to ensure DOM is ready for scrolling
    setTimeout(() => {
      this.scrollToFocused();
    }, 10);
  }

  close() {
    this.isOpen = false;
    this.dropdown.classList.remove('open');
    this.trigger.classList.remove('open');
    this.trigger.setAttribute('aria-expanded', 'false');
    this.focusedIndex = -1;

    // Clear focus styling
    const items = this.dropdown.querySelectorAll('.custom-select-item');
    items.forEach(item => item.classList.remove('focused'));
  }

  dispatchChangeEvent() {
    if (this.isMultiple) {
      const event = new CustomEvent('change', {
        detail: {
          selectedValues: this.selectedValues,
          selectedOptions: this.allOptions.filter(opt => this.selectedValues.includes(opt.value))
        }
      });
      this.element.dispatchEvent(event);
    } else {
      const selectedOption = this.allOptions.find(opt => opt.value === this.selectedValues);
      const event = new CustomEvent('change', {
        detail: {
          selectedValue: this.selectedValues,
          selectedOption: selectedOption
        }
      });
      this.element.dispatchEvent(event);
    }
  }

  // Public methods
  getSelectedValue() {
    return this.isMultiple ? this.selectedValues : this.selectedValues;
  }

  getSelectedValues() {
    return this.isMultiple ? this.selectedValues : [this.selectedValues].filter(Boolean);
  }

  setSelectedValue(value) {
    if (this.isMultiple) {
      this.selectedValues = Array.isArray(value) ? value : [value];
    } else {
      this.selectedValues = value;
    }
    this.updateDisplay();
    this.renderDropdown();
  }

  destroy() {
    // Remove from global instances tracking
    CustomSelect.instances.delete(this);

    // Close dropdown if open
    if (this.isOpen) {
      this.close();
    }

    // Remove event listeners to prevent memory leaks
    try {
      if (this.trigger && this.boundToggle) {
        this.trigger.removeEventListener('click', this.boundToggle);
        this.trigger.removeEventListener('keydown', this.boundHandleKeyDown);
      }

      if (this.dropdown && this.boundHandleDropdownClick) {
        this.dropdown.removeEventListener('click', this.boundHandleDropdownClick);
        this.dropdown.removeEventListener('keydown', this.boundHandleKeyDown);
      }

      if (this.chipsContainer && this.isMultiple && this.boundHandleChipClick) {
        this.chipsContainer.removeEventListener('click', this.boundHandleChipClick);
      }

      // Remove document event listener
      if (this.boundHandleDocumentClick) {
        document.removeEventListener('click', this.boundHandleDocumentClick);
      }
    } catch (error) {
      // Silently handle cases where elements might already be removed
      console.warn('CustomSelect: Error removing event listeners during destroy:', error);
    }

    // Remove DOM elements
    try {
      if (this.container && this.container.parentNode) {
        this.container.parentNode.removeChild(this.container);
      }
    } catch (error) {
      // Element might already be removed
      console.warn('CustomSelect: Error removing DOM elements during destroy:', error);
    }

    // Restore original element if it still exists
    try {
      if (this.element && document.contains(this.element)) {
        this.element.style.display = '';
      }
    } catch (error) {
      // Element might already be removed
      console.warn('CustomSelect: Error restoring original element during destroy:', error);
    }

    // Clear references
    this.element = null;
    this.container = null;
    this.trigger = null;
    this.dropdown = null;
    this.chipsContainer = null;
    this.allOptions = null;
    this.selectedValues = null;
    this.boundToggle = null;
    this.boundHandleDropdownClick = null;
    this.boundHandleKeyDown = null;
    this.boundHandleDocumentClick = null;
    this.boundHandleChipClick = null;
  }

  // Static method to close all dropdowns except the specified one
  static closeAllExcept(exceptInstance = null) {
    CustomSelect.instances.forEach(instance => {
      if (instance !== exceptInstance && instance.isOpen) {
        instance.close();
      }
    });
  }

  // Static method to close all dropdowns
  static closeAll() {
    CustomSelect.closeAllExcept(null);
  }

  // Static method to cleanup invalid instances (where DOM elements no longer exist)
  static cleanupInvalidInstances() {
    const invalidInstances = [];
    CustomSelect.instances.forEach(instance => {
      if (!instance.isValidInstance()) {
        invalidInstances.push(instance);
      }
    });

    invalidInstances.forEach(instance => {
      instance.destroy();
    });

    return invalidInstances.length;
  }

  // Check if this instance is still valid (DOM element exists)
  isValidInstance() {
    return this.element && document.contains(this.element);
  }

  // Static method to initialize multiple elements
  static init(selector, options = {}) {
    const elements = document.querySelectorAll(selector);
    const instances = [];

    elements.forEach(element => {
      instances.push(new CustomSelect(element, options));
    });

    return instances.length === 1 ? instances[0] : instances;
  }

  // Auto-initialize all select elements with data-select-type attribute
  static autoInit() {
    const selects = document.querySelectorAll('select[data-select-type]');
    const instances = [];

    selects.forEach(select => {
      const placeholder = select.dataset.placeholder;
      const options = placeholder ? { placeholder } : {};
      instances.push(new CustomSelect(select, options));
    });

    return instances;
  }
}

// Auto-initialize on DOM ready
if (typeof document !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => CustomSelect.autoInit());
  } else {
    CustomSelect.autoInit();
  }

  // Cleanup all instances on page unload to prevent memory leaks
  window.addEventListener('beforeunload', () => {
    CustomSelect.instances.forEach(instance => {
      instance.destroy();
    });
  });

  // Handle page visibility changes to close dropdowns when page becomes hidden
  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      CustomSelect.closeAll();
    }
  });

  // Periodic cleanup of invalid instances (every 30 seconds)
  setInterval(() => {
    const cleanedCount = CustomSelect.cleanupInvalidInstances();
    if (cleanedCount > 0) {
      console.log(`CustomSelect: Cleaned up ${cleanedCount} invalid instances`);
    }
  }, 30000);
}
