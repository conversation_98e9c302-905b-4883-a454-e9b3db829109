/**
 * CustomSelect - Unified Single and Multi-Select Component
 * Supports both single and multi-select dropdowns with keyboard navigation
 */
class CustomSelect {
  constructor(element, options = {}) {
    this.element = element;
    this.isMultiple = element.hasAttribute('multiple') || element.dataset.selectType === 'multi';

    this.options = {
      placeholder: this.isMultiple ? 'Select options' : 'Select an option',
      searchable: false,
      selectAll: true,
      maxHeight: '260px',
      ...options
    };

    this.selectedValues = this.isMultiple ? [] : null;
    this.allOptions = [];
    this.isOpen = false;
    this.focusedIndex = -1;

    this.init();
  }

  init() {
    this.parseOptions();
    this.createDropdown();
    this.attachEventListeners();
    this.updateDisplay();
  }

  parseOptions() {
    // Parse options from select element or data attribute
    if (this.element.tagName === 'SELECT') {
      const options = Array.from(this.element.querySelectorAll('option'));
      this.allOptions = options.map(opt => ({
        value: opt.value || opt.textContent.trim(),
        label: opt.textContent.trim(),
        selected: opt.selected
      }));
    } else if (this.element.dataset.options) {
      this.allOptions = JSON.parse(this.element.dataset.options);
    } else {
      this.allOptions = this.options.options || [];
    }

    // Set initially selected values
    if (this.isMultiple) {
      this.selectedValues = this.allOptions
        .filter(opt => opt.selected)
        .map(opt => opt.value);
    } else {
      const selectedOption = this.allOptions.find(opt => opt.selected);
      this.selectedValues = selectedOption ? selectedOption.value : null;
    }
  }

  createDropdown() {
    // Hide original element
    this.element.style.display = 'none';

    // Create container
    this.container = document.createElement('div');
    this.container.className = `custom-select-container ${this.isMultiple ? 'multi' : 'single'}`;

    // Create trigger
    this.trigger = document.createElement('div');
    this.trigger.className = 'custom-select-trigger';
    this.trigger.setAttribute('role', 'button');
    this.trigger.setAttribute('aria-haspopup', 'listbox');
    this.trigger.setAttribute('tabindex', '0');

    this.triggerText = document.createElement('span');
    this.triggerText.className = 'custom-select-text';

    this.arrow = document.createElement('span');
    this.arrow.className = 'custom-select-arrow';
    this.arrow.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
        <path d="M1 1.5L6 6.5L11 1.5" stroke="currentColor" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `;

    this.trigger.appendChild(this.triggerText);
    this.trigger.appendChild(this.arrow);

    // Create dropdown
    this.dropdown = document.createElement('div');
    this.dropdown.className = 'custom-select-dropdown';
    this.dropdown.setAttribute('role', 'listbox');
    if (this.isMultiple) {
      this.dropdown.setAttribute('aria-multiselectable', 'true');
    }

    // Create chips container for multi-select
    if (this.isMultiple) {
      this.chipsContainer = document.createElement('div');
      this.chipsContainer.className = 'custom-select-chips';
    }

    // Assemble
    this.container.appendChild(this.trigger);
    this.container.appendChild(this.dropdown);
    if (this.isMultiple) {
      this.container.appendChild(this.chipsContainer);
    }

    // Insert after original element
    this.element.parentNode.insertBefore(this.container, this.element.nextSibling);

    this.renderDropdown();
  }

  renderDropdown() {
    let html = '';

    // Add select all option if enabled (multi-select only)
    if (this.isMultiple && this.options.selectAll && this.allOptions.length > 1) {
      const allSelected = this.selectedValues.length === this.allOptions.length;
      html += `
        <div class="custom-select-item custom-select-select-all" role="option" data-value="__select_all__" data-index="-1">
          <input type="checkbox" id="select-all-${this.element.id}" class="custom-select-checkbox" ${allSelected ? 'checked' : ''} tabindex="-1">
          <label for="select-all-${this.element.id}" class="custom-select-item-text">Select All</label>
        </div>
      `;
    }

    // Add options
    this.allOptions.forEach((option, index) => {
      const isSelected = this.isMultiple
        ? this.selectedValues.includes(option.value)
        : this.selectedValues === option.value;

      if (this.isMultiple) {
        const checkboxId = `${this.element.id}-option-${index}`;
        html += `
          <div class="custom-select-item ${isSelected ? 'selected' : ''}" role="option" data-value="${option.value}" data-index="${index}">
            <input type="checkbox" id="${checkboxId}" class="custom-select-checkbox" ${isSelected ? 'checked' : ''} tabindex="-1">
            <label for="${checkboxId}" class="custom-select-item-text">${option.label}</label>
          </div>
        `;
      } else {
        html += `
          <div class="custom-select-item ${isSelected ? 'selected' : ''}"
               role="option"
               data-value="${option.value}"
               data-index="${index}"
               aria-selected="${isSelected}">
            <span class="custom-select-item-text">${option.label}</span>
          </div>
        `;
      }
    });

    // Wrap items in a container div
    this.dropdown.innerHTML = `<div class="custom-select-items-wrapper">${html}</div>`;
  }

  attachEventListeners() {
    // Trigger click
    this.trigger.addEventListener('click', (e) => {
      e.stopPropagation();
      this.toggle();
    });

    // Dropdown click
    this.dropdown.addEventListener('click', (e) => {
      e.stopPropagation();
      const item = e.target.closest('.custom-select-item');
      if (item) {
        this.handleItemClick(item);
      }
    });

    // Keyboard support
    this.trigger.addEventListener('keydown', (e) => {
      this.handleKeyDown(e);
    });

    // Keyboard navigation in dropdown
    this.dropdown.addEventListener('keydown', (e) => {
      this.handleKeyDown(e);
    });

    // Click outside to close
    document.addEventListener('click', () => {
      this.close();
    });

    // Chip removal (multi-select only)
    if (this.isMultiple) {
      this.chipsContainer.addEventListener('click', (e) => {
        if (e.target.classList.contains('custom-select-chip-remove')) {
          const value = e.target.dataset.value;
          this.toggleOption(value, false);
        }
      });
    }
  }

  handleKeyDown(e) {
    switch (e.key) {
      case 'Enter':
      case ' ':
        e.preventDefault();
        if (!this.isOpen) {
          this.open();
        } else if (this.focusedIndex >= 0) {
          // Select the focused item
          const items = this.dropdown.querySelectorAll('.custom-select-item');
          const focusedItem = items[this.focusedIndex];
          if (focusedItem) {
            this.handleItemClick(focusedItem);
            if (!this.isMultiple) {
              this.close();
            }
          }
        } else {
          this.toggle();
        }
        break;

      case 'ArrowDown':
        e.preventDefault();
        if (!this.isOpen) {
          this.open();
        } else {
          this.focusNext();
        }
        break;

      case 'ArrowUp':
        e.preventDefault();
        if (this.isOpen) {
          this.focusPrevious();
        }
        break;

      case 'Escape':
        e.preventDefault();
        this.close();
        this.trigger.focus();
        break;

      case 'Tab':
        if (this.isOpen) {
          this.close();
        }
        break;
    }
  }

  handleItemClick(item) {
    const value = item.dataset.value;

    if (value === '__select_all__') {
      this.toggleSelectAll();
    } else if (this.isMultiple) {
      const checkbox = item.querySelector('.custom-select-checkbox');
      this.toggleOption(value, !checkbox.checked);
    } else {
      this.selectOption(value);
      this.close();
    }
  }

  toggleSelectAll() {
    const allSelected = this.selectedValues.length === this.allOptions.length;

    if (allSelected) {
      this.selectedValues = [];
    } else {
      this.selectedValues = this.allOptions.map(opt => opt.value);
    }

    this.updateDisplay();
    this.renderDropdown();
    this.dispatchChangeEvent();
  }

  toggleOption(value, selected) {
    if (selected && !this.selectedValues.includes(value)) {
      this.selectedValues.push(value);
    } else if (!selected) {
      this.selectedValues = this.selectedValues.filter(v => v !== value);
    }

    this.updateDisplay();
    this.renderDropdown();
    this.dispatchChangeEvent();
  }

  selectOption(value) {
    this.selectedValues = value;
    this.updateDisplay();
    this.renderDropdown();
    this.dispatchChangeEvent();
  }

  updateDisplay() {
    if (this.isMultiple) {
      // Update trigger text for multi-select
      const count = this.selectedValues.length;
      if (count === 0) {
        this.triggerText.textContent = this.options.placeholder;
        this.triggerText.classList.add('placeholder');
      } else {
        this.triggerText.textContent = `${count} selected`;
        this.triggerText.classList.remove('placeholder');
      }

      // Update chips
      this.updateChips();

      // Update original select element
      if (this.element.tagName === 'SELECT') {
        Array.from(this.element.options).forEach(option => {
          option.selected = this.selectedValues.includes(option.value);
        });
      }
    } else {
      // Update trigger text for single-select
      const selectedOption = this.allOptions.find(opt => opt.value === this.selectedValues);
      if (selectedOption) {
        this.triggerText.textContent = selectedOption.label;
        this.triggerText.classList.remove('placeholder');
      } else {
        this.triggerText.textContent = this.options.placeholder;
        this.triggerText.classList.add('placeholder');
      }

      // Update original select element
      if (this.element.tagName === 'SELECT') {
        this.element.value = this.selectedValues || '';
        Array.from(this.element.options).forEach(option => {
          option.selected = option.value === this.selectedValues;
        });
      }
    }
  }

  updateChips() {
    this.chipsContainer.innerHTML = '';

    this.selectedValues.forEach(value => {
      const option = this.allOptions.find(opt => opt.value === value);
      if (option) {
        const chip = document.createElement('div');
        chip.className = 'custom-select-chip';
        chip.innerHTML = `
          ${option.label}
          <span class="custom-select-chip-remove" data-value="${value}">×</span>
        `;
        this.chipsContainer.appendChild(chip);
      }
    });
  }

  focusNext() {
    const items = this.dropdown.querySelectorAll('.custom-select-item');
    const maxIndex = items.length - 1;

    if (this.focusedIndex < maxIndex) {
      this.focusedIndex++;
    } else {
      this.focusedIndex = 0; // Wrap around
    }

    this.updateFocus();
    this.scrollToFocused();
  }

  focusPrevious() {
    const items = this.dropdown.querySelectorAll('.custom-select-item');
    const maxIndex = items.length - 1;

    if (this.focusedIndex > 0) {
      this.focusedIndex--;
    } else {
      this.focusedIndex = maxIndex; // Wrap around
    }

    this.updateFocus();
    this.scrollToFocused();
  }

  updateFocus() {
    const items = this.dropdown.querySelectorAll('.custom-select-item');
    items.forEach((item, index) => {
      item.classList.toggle('focused', index === this.focusedIndex);
    });
  }

  scrollToFocused() {
    const items = this.dropdown.querySelectorAll('.custom-select-item');
    const focusedItem = items[this.focusedIndex];
    if (focusedItem) {
      focusedItem.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
    }
  }

  toggle() {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  open() {
    this.isOpen = true;
    this.dropdown.classList.add('open');
    this.trigger.classList.add('open');
    this.trigger.setAttribute('aria-expanded', 'true');

    // Set focus to selected item or first item
    if (this.isMultiple) {
      this.focusedIndex = 0;
    } else {
      const selectedIndex = this.allOptions.findIndex(opt => opt.value === this.selectedValues);
      this.focusedIndex = selectedIndex >= 0 ? selectedIndex : 0;
    }

    this.updateFocus();
  }

  close() {
    this.isOpen = false;
    this.dropdown.classList.remove('open');
    this.trigger.classList.remove('open');
    this.trigger.setAttribute('aria-expanded', 'false');
    this.focusedIndex = -1;

    // Clear focus styling
    const items = this.dropdown.querySelectorAll('.custom-select-item');
    items.forEach(item => item.classList.remove('focused'));
  }

  dispatchChangeEvent() {
    if (this.isMultiple) {
      const event = new CustomEvent('change', {
        detail: {
          selectedValues: this.selectedValues,
          selectedOptions: this.allOptions.filter(opt => this.selectedValues.includes(opt.value))
        }
      });
      this.element.dispatchEvent(event);
    } else {
      const selectedOption = this.allOptions.find(opt => opt.value === this.selectedValues);
      const event = new CustomEvent('change', {
        detail: {
          selectedValue: this.selectedValues,
          selectedOption: selectedOption
        }
      });
      this.element.dispatchEvent(event);
    }
  }

  // Public methods
  getSelectedValue() {
    return this.isMultiple ? this.selectedValues : this.selectedValues;
  }

  getSelectedValues() {
    return this.isMultiple ? this.selectedValues : [this.selectedValues].filter(Boolean);
  }

  setSelectedValue(value) {
    if (this.isMultiple) {
      this.selectedValues = Array.isArray(value) ? value : [value];
    } else {
      this.selectedValues = value;
    }
    this.updateDisplay();
    this.renderDropdown();
  }

  destroy() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
    this.element.style.display = '';
  }

  // Static method to initialize multiple elements
  static init(selector, options = {}) {
    const elements = document.querySelectorAll(selector);
    const instances = [];

    elements.forEach(element => {
      instances.push(new CustomSelect(element, options));
    });

    return instances.length === 1 ? instances[0] : instances;
  }

  // Auto-initialize all select elements with data-select-type attribute
  static autoInit() {
    const selects = document.querySelectorAll('select[data-select-type]');
    const instances = [];

    selects.forEach(select => {
      const placeholder = select.dataset.placeholder;
      const options = placeholder ? { placeholder } : {};
      instances.push(new CustomSelect(select, options));
    });

    return instances;
  }
}

// Auto-initialize on DOM ready
if (typeof document !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => CustomSelect.autoInit());
  } else {
    CustomSelect.autoInit();
  }
}
